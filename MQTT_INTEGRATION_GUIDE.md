# 战场模拟器 MQTT 集成指南

## 📋 概述

战场模拟器现已支持通过 MQTT 协议发送实时模拟数据，除了原有的 HTTP API 接口外，还可以通过 MQTT 消息队列实时推送战场数据。

## 🚀 功能特性

### **双重数据发送**
- **HTTP API**: 继续支持原有的 REST API 接口
- **MQTT**: 新增实时消息队列推送

### **MQTT 数据类型**
1. **战场分析数据** (`battlefield/analysis`)
   - 每30秒发送一次
   - 包含敌我双方单位完整信息
   - 用于大模型分析

2. **三维显示数据** (`battlefield/threed/units`)
   - 每2.4秒发送一次（每分钟25次）
   - 简化的单位位置和状态信息
   - 用于实时三维显示

3. **单位状态更新** (`battlefield/units/status/{side}/{unit_id}`)
   - 随机发送（20%概率）
   - 单个单位的详细状态变化
   - 用于精细化监控

## 📦 安装依赖

```bash
# 安装 MQTT 客户端库
pip install paho-mqtt==1.6.1

# 或者安装所有依赖
pip install -r requirements.txt
```

## ⚙️ 配置说明

### **MQTT 配置文件**
配置文件位置: `config/mqtt_config.json`

```json
{
  "mqtt": {
    "broker_host": "localhost",
    "broker_port": 1883,
    "username": null,
    "password": null,
    "keepalive": 60,
    "client_id": "battlefield_simulator"
  },
  "topics": {
    "battlefield_analysis": "battlefield/analysis",
    "threed_units": "battlefield/threed/units",
    "unit_status": "battlefield/units/status"
  },
  "message_settings": {
    "qos": 1,
    "retain": false
  }
}
```

### **配置参数说明**
- `broker_host`: MQTT 代理服务器地址
- `broker_port`: MQTT 代理服务器端口（默认1883）
- `username/password`: MQTT 认证信息（可选）
- `qos`: 消息质量等级（0-2）
- `retain`: 是否保留消息

## 🛠️ 使用方法

### **1. 启动 MQTT 代理**
```bash
# 使用 Mosquitto（推荐）
sudo apt-get install mosquitto mosquitto-clients
sudo systemctl start mosquitto

# 或使用 Docker
docker run -it -p 1883:1883 eclipse-mosquitto
```

### **2. 启动战场模拟器**
```bash
python battlefield_simulator.py
```

### **3. 测试 MQTT 数据接收**
```bash
# 使用提供的测试订阅器
python mqtt_test_subscriber.py

# 或使用 mosquitto 客户端工具
mosquitto_sub -h localhost -t "battlefield/#" -v
```

## 📡 MQTT 主题结构

```
battlefield/
├── analysis                    # 战场分析数据
├── threed/
│   └── units                  # 三维显示数据
├── units/
│   └── status/
│       ├── 敌方/
│       │   ├── ENEMY_001      # 敌方单位状态
│       │   ├── ENEMY_002
│       │   └── ENEMY_003
│       └── 我方/
│           ├── FRIENDLY_001   # 我方单位状态
│           └── FRIENDLY_002
└── system/
    └── status                 # 系统状态（可选）
```

## 📊 数据格式示例

### **战场分析数据**
```json
{
  "timestamp": "2025-09-28T10:30:00",
  "simulation_time": 15.0,
  "enemy_units": [...],
  "friendly_units": [...],
  "battlefield_status": {
    "total_enemy": 3,
    "total_friendly": 2,
    "active_threats": 2
  }
}
```

### **单位状态更新**
```json
{
  "timestamp": "2025-09-28T10:30:00",
  "unit": {
    "id": "ENEMY_001",
    "name": "T-90主战坦克",
    "type": "主战坦克",
    "side": "敌方",
    "position": {
      "longitude": 116.3974,
      "latitude": 39.9042,
      "altitude": 45.2
    },
    "status": {
      "health": 95.0,
      "ammo": 80.0,
      "fuel": 85.0,
      "operational": true
    }
  }
}
```

## 🔧 自定义配置

### **修改 MQTT 代理地址**
编辑 `config/mqtt_config.json`:
```json
{
  "mqtt": {
    "broker_host": "*************",
    "broker_port": 1883
  }
}
```

### **添加认证**
```json
{
  "mqtt": {
    "broker_host": "localhost",
    "broker_port": 1883,
    "username": "your_username",
    "password": "your_password"
  }
}
```

### **自定义主题**
```json
{
  "topics": {
    "battlefield_analysis": "custom/battlefield/analysis",
    "threed_units": "custom/threed/units",
    "unit_status": "custom/units/status"
  }
}
```

## 🧪 测试验证

### **1. 启动完整测试**
```bash
# 终端1: 启动 MQTT 代理
mosquitto

# 终端2: 启动测试订阅器
python mqtt_test_subscriber.py

# 终端3: 启动战场模拟器
python battlefield_simulator.py
```

### **2. 验证数据流**
观察测试订阅器的输出，应该看到：
- ✅ MQTT 连接成功
- ✅ 订阅主题成功
- ✅ 定期接收战场分析数据（30秒间隔）
- ✅ 高频接收三维显示数据（2.4秒间隔）
- ✅ 随机接收单位状态更新

## 🚨 故障排除

### **MQTT 连接失败**
```bash
# 检查 MQTT 代理是否运行
sudo systemctl status mosquitto

# 检查端口是否开放
netstat -an | grep 1883

# 测试连接
mosquitto_pub -h localhost -t test -m "hello"
```

### **没有收到数据**
1. 检查 MQTT 配置文件是否正确
2. 确认主题订阅是否成功
3. 查看模拟器日志中的 MQTT 连接状态
4. 验证防火墙设置

### **数据格式错误**
1. 检查 JSON 解析是否成功
2. 确认字符编码为 UTF-8
3. 验证数据完整性

## 📈 性能优化

### **高频数据处理**
- 三维显示数据每2.4秒发送一次，适合实时显示
- 可根据需要调整发送频率
- 建议使用 QoS 1 确保消息送达

### **网络优化**
- 使用本地 MQTT 代理减少延迟
- 配置适当的 keepalive 时间
- 考虑使用 MQTT 集群提高可用性

## 🔗 集成建议

### **与现有系统集成**
1. **实时监控系统**: 订阅单位状态主题
2. **数据分析平台**: 订阅战场分析主题
3. **可视化系统**: 订阅三维显示主题
4. **告警系统**: 监控关键状态变化

### **扩展功能**
- 添加更多自定义主题
- 实现双向 MQTT 通信
- 集成 MQTT 持久化存储
- 添加 MQTT 安全认证
