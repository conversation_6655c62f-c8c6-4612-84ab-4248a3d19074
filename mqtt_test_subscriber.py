#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT测试订阅器
用于测试战场模拟器的MQTT数据发送功能
"""
import json
import logging
import paho.mqtt.client as mqtt
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MQTTTestSubscriber:
    """MQTT测试订阅器"""
    
    def __init__(self, broker_host="localhost", broker_port=1883):
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.client = None
        self.message_count = 0
        
        # 订阅的主题
        self.topics = [
            "battlefield/analysis",
            "battlefield/threed/units", 
            "battlefield/units/status/+/+",  # 通配符订阅所有单位状态
            "battlefield/system/status"
        ]
    
    def on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            logger.info("✅ MQTT连接成功")
            # 订阅所有主题
            for topic in self.topics:
                client.subscribe(topic, qos=1)
                logger.info(f"📡 已订阅主题: {topic}")
        else:
            logger.error(f"❌ MQTT连接失败，返回码: {rc}")
    
    def on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        if rc != 0:
            logger.warning("⚠️ MQTT意外断开连接")
        else:
            logger.info("✅ MQTT正常断开连接")
    
    def on_message(self, client, userdata, msg):
        """消息接收回调"""
        self.message_count += 1
        topic = msg.topic
        
        try:
            # 解析JSON数据
            data = json.loads(msg.payload.decode('utf-8'))
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            logger.info(f"📨 [{timestamp}] 收到消息 #{self.message_count}")
            logger.info(f"   主题: {topic}")
            logger.info(f"   QoS: {msg.qos}")
            logger.info(f"   保留: {msg.retain}")
            
            # 根据主题类型显示不同信息
            if "analysis" in topic:
                self._handle_analysis_message(data)
            elif "threed/units" in topic:
                self._handle_threed_message(data)
            elif "units/status" in topic:
                self._handle_unit_status_message(data)
            else:
                logger.info(f"   数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON解析失败: {e}")
            logger.error(f"   原始数据: {msg.payload.decode('utf-8')[:200]}...")
        except Exception as e:
            logger.error(f"❌ 处理消息时发生错误: {e}")
    
    def _handle_analysis_message(self, data):
        """处理战场分析消息"""
        logger.info("   类型: 🎯 战场分析数据")
        if 'enemy_units' in data:
            logger.info(f"   敌方单位数量: {len(data['enemy_units'])}")
        if 'friendly_units' in data:
            logger.info(f"   我方单位数量: {len(data['friendly_units'])}")
        if 'battlefield_status' in data:
            status = data['battlefield_status']
            logger.info(f"   战场状态: 敌方{status.get('total_enemy', 0)}个, 我方{status.get('total_friendly', 0)}个")
    
    def _handle_threed_message(self, data):
        """处理三维显示消息"""
        logger.info("   类型: 🗺️ 三维显示数据")
        if 'objectList' in data:
            logger.info(f"   目标数量: {len(data['objectList'])}")
        if 'friendly_units' in data:
            logger.info(f"   友军数量: {len(data['friendly_units'])}")
    
    def _handle_unit_status_message(self, data):
        """处理单位状态消息"""
        logger.info("   类型: 🚁 单位状态更新")
        if 'unit' in data:
            unit = data['unit']
            logger.info(f"   单位: {unit.get('name', 'Unknown')} ({unit.get('side', 'Unknown')})")
            if 'status' in unit:
                status = unit['status']
                logger.info(f"   状态: 血量{status.get('health', 0):.1f}% 燃料{status.get('fuel', 0):.1f}% 弹药{status.get('ammo', 0):.1f}%")
    
    def start(self):
        """启动订阅器"""
        try:
            self.client = mqtt.Client(client_id="mqtt_test_subscriber")
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_message = self.on_message
            
            logger.info(f"🚀 连接到MQTT代理: {self.broker_host}:{self.broker_port}")
            self.client.connect(self.broker_host, self.broker_port, 60)
            
            # 开始循环
            logger.info("📡 开始监听MQTT消息...")
            logger.info("按 Ctrl+C 停止")
            self.client.loop_forever()
            
        except KeyboardInterrupt:
            logger.info("🛑 收到停止信号")
        except Exception as e:
            logger.error(f"❌ 启动失败: {e}")
        finally:
            if self.client:
                self.client.disconnect()
            logger.info(f"📊 总共接收到 {self.message_count} 条消息")

def main():
    """主程序"""
    # 创建并启动订阅器
    subscriber = MQTTTestSubscriber()
    subscriber.start()

if __name__ == "__main__":
    main()
