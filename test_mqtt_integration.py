#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT集成测试脚本
测试战场模拟器的MQTT功能是否正常工作
"""
import asyncio
import json
import logging
import time
from battlefield_simulator import BattlefieldSimulator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mqtt_integration():
    """测试MQTT集成功能"""
    logger.info("=== MQTT集成测试开始 ===")
    
    # 测试配置
    test_mqtt_config = {
        "broker_host": "localhost",
        "broker_port": 1883,
        "username": None,
        "password": None,
        "topics": {
            "battlefield_analysis": "test/battlefield/analysis",
            "threed_units": "test/battlefield/threed/units",
            "unit_status": "test/battlefield/units/status"
        },
        "qos": 1,
        "retain": False,
        "keepalive": 60,
        "client_id": "test_battlefield_simulator"
    }
    
    try:
        # 创建模拟器实例
        logger.info("1. 创建战场模拟器...")
        simulator = BattlefieldSimulator(
            api_url="http://localhost:8010",
            mqtt_config=test_mqtt_config
        )
        
        # 等待MQTT连接建立
        logger.info("2. 等待MQTT连接建立...")
        await asyncio.sleep(2)
        
        if simulator.mqtt_connected:
            logger.info("✅ MQTT连接成功")
        else:
            logger.warning("⚠️ MQTT连接失败，但测试继续进行")
        
        # 测试数据创建
        logger.info("3. 测试数据创建...")
        battlefield_data = simulator._create_battlefield_data()
        threed_data = simulator._create_threed_data()
        
        logger.info(f"   战场数据包含 {len(battlefield_data['enemy_units'])} 个敌方单位")
        logger.info(f"   三维数据包含 {len(threed_data['objectList'])} 个目标")
        
        # 测试MQTT发送
        logger.info("4. 测试MQTT数据发送...")
        
        # 发送战场分析数据
        success1 = simulator._publish_to_mqtt(
            test_mqtt_config["topics"]["battlefield_analysis"],
            battlefield_data,
            "测试战场分析数据"
        )
        
        # 发送三维显示数据
        success2 = simulator._publish_to_mqtt(
            test_mqtt_config["topics"]["threed_units"],
            threed_data,
            "测试三维显示数据"
        )
        
        # 发送单位状态数据
        if simulator.enemy_units:
            unit = simulator.enemy_units[0]
            unit_data = {
                "timestamp": time.time(),
                "unit": {
                    "id": unit.id,
                    "name": unit.name,
                    "side": unit.side,
                    "status": {
                        "health": unit.status.health,
                        "ammo": unit.status.ammo,
                        "fuel": unit.status.fuel,
                        "operational": unit.status.operational
                    }
                }
            }
            
            success3 = simulator._publish_to_mqtt(
                f"{test_mqtt_config['topics']['unit_status']}/{unit.side}/{unit.id}",
                unit_data,
                "测试单位状态数据"
            )
        else:
            success3 = False
            logger.warning("   没有敌方单位可测试")
        
        # 测试结果
        logger.info("5. 测试结果:")
        logger.info(f"   战场分析数据发送: {'✅ 成功' if success1 else '❌ 失败'}")
        logger.info(f"   三维显示数据发送: {'✅ 成功' if success2 else '❌ 失败'}")
        logger.info(f"   单位状态数据发送: {'✅ 成功' if success3 else '❌ 失败'}")
        
        # 短时间运行模拟
        logger.info("6. 运行短时间模拟测试...")
        logger.info("   (运行10秒，观察数据发送)")
        
        # 启动模拟，但只运行10秒
        simulation_task = asyncio.create_task(
            simulator.start_simulation(
                analysis_interval_seconds=5,  # 5秒间隔
                threed_interval_seconds=2     # 2秒间隔
            )
        )
        
        # 等待10秒
        await asyncio.sleep(10)
        
        # 停止模拟
        simulator.stop_simulation()
        
        # 等待任务完成
        try:
            await asyncio.wait_for(simulation_task, timeout=2)
        except asyncio.TimeoutError:
            simulation_task.cancel()
        
        logger.info("✅ 短时间模拟测试完成")
        
        # 总结
        logger.info("=== MQTT集成测试完成 ===")
        if simulator.mqtt_connected and (success1 or success2):
            logger.info("🎉 MQTT集成测试成功！")
            return True
        else:
            logger.warning("⚠️ MQTT集成测试部分失败，请检查MQTT代理是否运行")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False
    
    finally:
        # 清理
        if 'simulator' in locals():
            simulator.stop_simulation()

def main():
    """主程序"""
    print("MQTT集成测试")
    print("=" * 50)
    print("此测试将验证战场模拟器的MQTT功能")
    print("请确保MQTT代理(如Mosquitto)正在运行")
    print("=" * 50)
    
    # 运行测试
    result = asyncio.run(test_mqtt_integration())
    
    if result:
        print("\n🎉 测试成功！MQTT集成工作正常")
        print("\n下一步:")
        print("1. 启动MQTT订阅器: python mqtt_test_subscriber.py")
        print("2. 启动战场模拟器: python battlefield_simulator.py")
        print("3. 观察实时数据流")
    else:
        print("\n⚠️ 测试失败，请检查:")
        print("1. MQTT代理是否运行 (mosquitto)")
        print("2. 网络连接是否正常")
        print("3. 配置文件是否正确")

if __name__ == "__main__":
    main()
