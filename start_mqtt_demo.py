#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT演示启动脚本
快速启动战场模拟器的MQTT功能演示
"""
import asyncio
import logging
import subprocess
import sys
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖是否安装"""
    logger.info("检查依赖...")
    
    try:
        import paho.mqtt.client as mqtt
        logger.info("✅ paho-mqtt 已安装")
    except ImportError:
        logger.error("❌ paho-mqtt 未安装")
        logger.info("请运行: pip install paho-mqtt==1.6.1")
        return False
    
    try:
        import requests
        logger.info("✅ requests 已安装")
    except ImportError:
        logger.error("❌ requests 未安装")
        logger.info("请运行: pip install requests")
        return False
    
    return True

def check_mqtt_broker():
    """检查MQTT代理是否运行"""
    logger.info("检查MQTT代理...")
    
    try:
        import paho.mqtt.client as mqtt
        
        # 创建测试客户端
        client = mqtt.Client(client_id="mqtt_check")
        
        # 设置连接超时
        connected = False
        
        def on_connect(client, userdata, flags, rc):
            nonlocal connected
            connected = (rc == 0)
        
        client.on_connect = on_connect
        
        # 尝试连接
        client.connect("localhost", 1883, 5)
        client.loop_start()
        
        # 等待连接结果
        time.sleep(2)
        client.loop_stop()
        client.disconnect()
        
        if connected:
            logger.info("✅ MQTT代理运行正常")
            return True
        else:
            logger.warning("⚠️ MQTT代理连接失败")
            return False
            
    except Exception as e:
        logger.warning(f"⚠️ MQTT代理检查失败: {e}")
        return False

def start_mosquitto():
    """尝试启动Mosquitto MQTT代理"""
    logger.info("尝试启动Mosquitto...")
    
    try:
        # 尝试使用systemctl启动
        result = subprocess.run(
            ["sudo", "systemctl", "start", "mosquitto"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            logger.info("✅ Mosquitto已启动")
            time.sleep(2)  # 等待服务启动
            return True
        else:
            logger.warning("⚠️ 无法通过systemctl启动Mosquitto")
            
    except (subprocess.TimeoutExpired, FileNotFoundError):
        logger.warning("⚠️ systemctl不可用")
    
    try:
        # 尝试直接启动mosquitto
        logger.info("尝试直接启动mosquitto...")
        process = subprocess.Popen(
            ["mosquitto"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        time.sleep(2)  # 等待启动
        
        if process.poll() is None:  # 进程仍在运行
            logger.info("✅ Mosquitto已直接启动")
            return True
        else:
            logger.warning("⚠️ Mosquitto启动失败")
            
    except FileNotFoundError:
        logger.warning("⚠️ mosquitto命令不存在")
    
    return False

def show_installation_guide():
    """显示安装指南"""
    print("\n" + "="*60)
    print("MQTT代理安装指南")
    print("="*60)
    print("\n🐧 Ubuntu/Debian:")
    print("sudo apt-get update")
    print("sudo apt-get install mosquitto mosquitto-clients")
    print("sudo systemctl start mosquitto")
    print("sudo systemctl enable mosquitto")
    
    print("\n🍎 macOS:")
    print("brew install mosquitto")
    print("brew services start mosquitto")
    
    print("\n🐳 Docker:")
    print("docker run -it -p 1883:1883 eclipse-mosquitto")
    
    print("\n🪟 Windows:")
    print("下载并安装: https://mosquitto.org/download/")
    print("="*60)

async def run_demo():
    """运行演示"""
    logger.info("=== 战场模拟器 MQTT 演示 ===")
    
    # 检查依赖
    if not check_dependencies():
        logger.error("❌ 依赖检查失败，请安装必要的依赖")
        return False
    
    # 检查MQTT代理
    if not check_mqtt_broker():
        logger.info("尝试启动MQTT代理...")
        if not start_mosquitto():
            logger.error("❌ 无法启动MQTT代理")
            show_installation_guide()
            return False
        
        # 再次检查
        if not check_mqtt_broker():
            logger.error("❌ MQTT代理仍然无法连接")
            return False
    
    # 导入并运行模拟器
    try:
        from battlefield_simulator import BattlefieldSimulator
        
        logger.info("🚀 启动战场模拟器...")
        
        # 创建模拟器
        simulator = BattlefieldSimulator()
        
        # 等待MQTT连接
        await asyncio.sleep(2)
        
        if not simulator.mqtt_connected:
            logger.warning("⚠️ MQTT连接失败，但继续运行演示")
        
        logger.info("📡 开始MQTT数据发送演示...")
        logger.info("   - 战场分析数据: 每30秒")
        logger.info("   - 三维显示数据: 每2.4秒")
        logger.info("   - 单位状态更新: 随机")
        logger.info("\n💡 提示:")
        logger.info("   在另一个终端运行: python mqtt_test_subscriber.py")
        logger.info("   来查看实时MQTT数据")
        logger.info("\n按 Ctrl+C 停止演示")
        
        # 运行模拟
        try:
            await simulator.start_simulation(
                analysis_interval_seconds=30,
                threed_interval_seconds=2.4
            )
        except KeyboardInterrupt:
            logger.info("🛑 收到停止信号")
        finally:
            simulator.stop_simulation()
            logger.info("✅ 演示结束")
            
        return True
        
    except ImportError as e:
        logger.error(f"❌ 导入模拟器失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 运行演示时发生错误: {e}")
        return False

def main():
    """主程序"""
    print("🎯 战场模拟器 MQTT 演示")
    print("=" * 50)
    print("此演示将启动战场模拟器并通过MQTT发送实时数据")
    print("=" * 50)
    
    try:
        success = asyncio.run(run_demo())
        
        if success:
            print("\n🎉 演示成功完成！")
            print("\n📚 更多信息请查看:")
            print("   - MQTT_INTEGRATION_GUIDE.md")
            print("   - config/mqtt_config.json")
        else:
            print("\n❌ 演示失败")
            print("\n🔧 故障排除:")
            print("   1. 确保安装了所有依赖: pip install -r requirements.txt")
            print("   2. 确保MQTT代理正在运行")
            print("   3. 检查网络连接")
            
    except KeyboardInterrupt:
        print("\n🛑 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")

if __name__ == "__main__":
    main()
