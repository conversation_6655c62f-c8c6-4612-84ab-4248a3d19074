#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战场单位模拟器
每分钟发送一次敌方和我方单位的位置和状态变化数据
"""
import asyncio
import json
import time
import random
import math
import logging
import requests
import paho.mqtt.client as mqtt
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Position:
    """位置信息"""
    longitude: float  # 经度
    latitude: float   # 纬度
    altitude: float   # 高度

@dataclass
class UnitStatus:
    """单位状态"""
    health: float      # 生命值 (0-100)
    ammo: float       # 弹药状态 (0-100)
    fuel: float       # 燃料状态 (0-100)
    operational: bool  # 是否可操作

@dataclass
class BattlefieldUnit:
    """战场单位"""
    id: str
    name: str          # 武器名称
    type: str          # 武器类型
    side: str          # 属方 (敌方/我方)
    position: Position
    status: UnitStatus
    threat_level: str  # 威胁等级
    confidence: float  # 置信度
    last_seen: str     # 最后发现时间
    speed: float       # 移动速度 (km/h)
    heading: float     # 航向角度 (0-360度)

class BattlefieldSimulator:
    """战场模拟器"""

    def __init__(self, api_url: str = "http://localhost:8010",
                 mqtt_config: Optional[Dict[str, Any]] = None,
                 mqtt_config_file: str = "config/mqtt_config.json"):
        self.api_url = api_url
        self.enemy_units = []
        self.friendly_units = []
        self.simulation_time = 0
        self.running = False

        # 加载MQTT配置
        self.mqtt_config = self._load_mqtt_config(mqtt_config, mqtt_config_file)

        # MQTT客户端
        self.mqtt_client = None
        self.mqtt_connected = False

        # 初始化单位
        self._initialize_units()

        # 初始化MQTT连接
        self._initialize_mqtt()

    def _load_mqtt_config(self, mqtt_config: Optional[Dict[str, Any]],
                         config_file: str) -> Dict[str, Any]:
        """加载MQTT配置"""
        # 默认配置
        default_config = {
            "broker_host": "localhost",
            "broker_port": 1883,
            "username": None,
            "password": None,
            "topics": {
                "battlefield_analysis": "battlefield/analysis",
                "threed_units": "battlefield/threed/units",
                "unit_status": "battlefield/units/status"
            },
            "qos": 1,
            "retain": False,
            "keepalive": 60,
            "client_id": "battlefield_simulator"
        }

        # 如果直接传入了配置，使用传入的配置
        if mqtt_config:
            return {**default_config, **mqtt_config}

        # 尝试从配置文件加载
        try:
            import os
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)

                # 合并配置
                merged_config = default_config.copy()
                if 'mqtt' in file_config:
                    merged_config.update(file_config['mqtt'])
                if 'topics' in file_config:
                    merged_config['topics'].update(file_config['topics'])
                if 'message_settings' in file_config:
                    merged_config.update(file_config['message_settings'])

                logger.info(f"已从配置文件加载MQTT配置: {config_file}")
                return merged_config
            else:
                logger.warning(f"MQTT配置文件不存在: {config_file}，使用默认配置")

        except Exception as e:
            logger.error(f"加载MQTT配置文件失败: {e}，使用默认配置")

        return default_config
    
    def _initialize_units(self):
        """初始化战场单位"""
        # 创建3个敌方单位
        self.enemy_units = [
            BattlefieldUnit(
                id="ENEMY_001",
                name="T-90主战坦克",
                type="主战坦克",
                side="敌方",
                position=Position(116.3974, 39.9042, 45.2),
                status=UnitStatus(100.0, 85.0, 90.0, True),
                threat_level="高",
                confidence=0.95,
                last_seen=datetime.now().isoformat(),
                speed=25.0,  # km/h
                heading=45.0
            ),
            BattlefieldUnit(
                id="ENEMY_002",
                name="BTR-80装甲运兵车",
                type="装甲车",
                side="敌方",
                position=Position(116.4174, 39.9242, 52.1),
                status=UnitStatus(100.0, 70.0, 80.0, True),
                threat_level="中",
                confidence=0.88,
                last_seen=datetime.now().isoformat(),
                speed=35.0,
                heading=120.0
            ),
            BattlefieldUnit(
                id="ENEMY_003",
                name="苏-35战斗机",
                type="战斗机",
                side="敌方",
                position=Position(116.3574, 39.8842, 8500.0),
                status=UnitStatus(100.0, 90.0, 75.0, True),
                threat_level="极高",
                confidence=0.92,
                last_seen=datetime.now().isoformat(),
                speed=800.0,
                heading=270.0
            )
        ]
        
        # 创建2个我方单位
        self.friendly_units = [
            BattlefieldUnit(
                id="FRIENDLY_001",
                name="99A主战坦克",
                type="主战坦克",
                side="我方",
                position=Position(116.3774, 39.8942, 48.5),
                status=UnitStatus(100.0, 95.0, 90.0, True),
                threat_level="",
                confidence=1.0,
                last_seen=datetime.now().isoformat(),
                speed=30.0,
                heading=315.0
            ),
            BattlefieldUnit(
                id="FRIENDLY_002",
                name="歼-20战斗机",
                type="战斗机",
                side="我方",
                position=Position(116.3674, 39.8742, 9200.0),
                status=UnitStatus(100.0, 100.0, 85.0, True),
                threat_level="",
                confidence=1.0,
                last_seen=datetime.now().isoformat(),
                speed=900.0,
                heading=180.0
            )
        ]
        
        logger.info(f"初始化完成: {len(self.enemy_units)}个敌方单位, {len(self.friendly_units)}个我方单位")

    def _initialize_mqtt(self):
        """初始化MQTT连接"""
        try:
            # 创建MQTT客户端
            client_id = self.mqtt_config.get("client_id", "battlefield_simulator")
            self.mqtt_client = mqtt.Client(client_id=client_id)

            # 设置回调函数
            self.mqtt_client.on_connect = self._on_mqtt_connect
            self.mqtt_client.on_disconnect = self._on_mqtt_disconnect
            self.mqtt_client.on_publish = self._on_mqtt_publish

            # 设置用户名和密码（如果提供）
            if self.mqtt_config.get("username") and self.mqtt_config.get("password"):
                self.mqtt_client.username_pw_set(
                    self.mqtt_config["username"],
                    self.mqtt_config["password"]
                )

            # 连接到MQTT代理
            keepalive = self.mqtt_config.get("keepalive", 60)
            self.mqtt_client.connect(
                self.mqtt_config["broker_host"],
                self.mqtt_config["broker_port"],
                keepalive
            )

            # 启动网络循环
            self.mqtt_client.loop_start()

            logger.info(f"MQTT客户端初始化完成")
            logger.info(f"  - 代理地址: {self.mqtt_config['broker_host']}:{self.mqtt_config['broker_port']}")
            logger.info(f"  - 客户端ID: {client_id}")
            logger.info(f"  - 保活时间: {keepalive}秒")

        except Exception as e:
            logger.error(f"MQTT初始化失败: {e}")
            self.mqtt_client = None

    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            self.mqtt_connected = True
            logger.info("MQTT连接成功")
        else:
            self.mqtt_connected = False
            logger.error(f"MQTT连接失败，返回码: {rc}")

    def _on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.mqtt_connected = False
        if rc != 0:
            logger.warning("MQTT意外断开连接")
        else:
            logger.info("MQTT正常断开连接")

    def _on_mqtt_publish(self, client, userdata, mid):
        """MQTT发布消息回调"""
        logger.debug(f"MQTT消息发布成功，消息ID: {mid}")

    def _publish_to_mqtt(self, topic: str, data: Dict[str, Any], description: str = "数据"):
        """发布数据到MQTT"""
        if not self.mqtt_client or not self.mqtt_connected:
            logger.warning(f"MQTT未连接，跳过{description}发送")
            return False

        try:
            # 将数据转换为JSON字符串
            payload = json.dumps(data, ensure_ascii=False, default=str)

            # 发布消息
            result = self.mqtt_client.publish(
                topic,
                payload,
                qos=self.mqtt_config["qos"],
                retain=self.mqtt_config["retain"]
            )

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"✅ {description}已发送到MQTT主题: {topic}")
                return True
            else:
                logger.error(f"❌ {description}发送失败，错误码: {result.rc}")
                return False

        except Exception as e:
            logger.error(f"❌ 发送{description}到MQTT时发生错误: {e}")
            return False
    
    def _update_unit_position(self, unit: BattlefieldUnit, time_delta_minutes: float):
        """更新单位位置"""
        # 计算移动距离 (km)
        distance_km = (unit.speed * time_delta_minutes) / 60.0
        
        # 转换为经纬度变化 (粗略计算)
        # 1度经度 ≈ 111km (在北纬39度附近约为85km)
        # 1度纬度 ≈ 111km
        lat_change = (distance_km * math.cos(math.radians(unit.heading))) / 111.0
        lon_change = (distance_km * math.sin(math.radians(unit.heading))) / 85.0
        
        unit.position.latitude += lat_change
        unit.position.longitude += lon_change
        
        # 添加随机扰动
        unit.position.latitude += random.uniform(-0.0001, 0.0001)
        unit.position.longitude += random.uniform(-0.0001, 0.0001)
        
        # 飞行器高度变化
        if unit.type == "战斗机":
            unit.position.altitude += random.uniform(-100, 100)
            unit.position.altitude = max(1000, min(15000, unit.position.altitude))
    
    def _update_unit_status(self, unit: BattlefieldUnit):
        """更新单位状态"""
        # 模拟燃料消耗
        fuel_consumption = random.uniform(0.5, 2.0)
        unit.status.fuel = max(0, unit.status.fuel - fuel_consumption)
        
        # 模拟弹药消耗 (战斗时)
        if random.random() < 0.1:  # 10%概率发生战斗
            ammo_consumption = random.uniform(2.0, 8.0)
            unit.status.ammo = max(0, unit.status.ammo - ammo_consumption)
        
        # 模拟损伤
        if random.random() < 0.05:  # 5%概率受到损伤
            damage = random.uniform(1.0, 10.0)
            unit.status.health = max(0, unit.status.health - damage)
        
        # 更新操作状态
        unit.status.operational = (
            unit.status.health > 20 and 
            unit.status.fuel > 5 and 
            unit.status.ammo > 0
        )
        
        # 随机改变航向
        if random.random() < 0.3:  # 30%概率改变航向
            unit.heading += random.uniform(-30, 30)
            unit.heading = unit.heading % 360
        
        # 更新最后发现时间
        unit.last_seen = datetime.now().isoformat()
        
        # 更新置信度
        if unit.side == "敌方":
            unit.confidence = max(0.5, unit.confidence + random.uniform(-0.05, 0.02))

        # 发送单位状态到MQTT（如果有重要变化）
        if random.random() < 0.2:  # 20%概率发送单位状态更新
            self._send_unit_status_to_mqtt(unit)
    
    def _create_battlefield_data(self) -> Dict[str, Any]:
        """创建战场数据包"""
        return {
            "timestamp": datetime.now().isoformat(),
            "simulation_time": self.simulation_time,
            "enemy_units": [asdict(unit) for unit in self.enemy_units],
            "friendly_units": [asdict(unit) for unit in self.friendly_units],
            "battlefield_status": {
                "total_enemy": len([u for u in self.enemy_units if u.status.operational]),
                "total_friendly": len([u for u in self.friendly_units if u.status.operational]),
                "active_threats": len([u for u in self.enemy_units if u.status.operational and u.threat_level in ["高", "极高"]])
            }
        }
    
    async def _send_data_to_api(self, data: Dict[str, Any]):
        """发送数据到API和MQTT"""
        # 发送到HTTP API
        try:
            response = requests.post(
                f"{self.api_url}/battlefield/analyze",
                json=data,
                timeout=30
            )
            if response.status_code == 200:
                logger.info(f"数据发送成功 - 响应时间: {response.elapsed.total_seconds():.2f}s")
                result = response.json()
                logger.info(f"分析结果状态: {result.get('status', 'unknown')}")
            else:
                logger.error(f"API调用失败 - 状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"发送数据失败: {e}")
        except Exception as e:
            logger.error(f"发送数据异常: {e}")

        # 发送到MQTT
        self._publish_to_mqtt(
            self.mqtt_config["topics"]["battlefield_analysis"],
            data,
            "战场分析数据"
        )
    
    def update_simulation(self, time_delta_minutes: float = 1.0):
        """更新模拟状态"""
        self.simulation_time += time_delta_minutes
        
        logger.info(f"=== 模拟时间: {self.simulation_time:.1f}分钟 ===")
        
        # 更新所有单位
        all_units = self.enemy_units + self.friendly_units
        for unit in all_units:
            if unit.status.operational:
                self._update_unit_position(unit, time_delta_minutes)
                self._update_unit_status(unit)
                
                logger.info(f"{unit.side} {unit.name}: "
                          f"位置({unit.position.longitude:.4f}, {unit.position.latitude:.4f}), "
                          f"状态(血量:{unit.status.health:.1f}%, 燃料:{unit.status.fuel:.1f}%, "
                          f"弹药:{unit.status.ammo:.1f}%)")

    def _create_threed_data(self) -> Dict[str, Any]:
        """创建三维显示数据包（简化版本）"""
        return {
            "code": 0,
            "id": -6,
            "seq":"26543322322",
            "time": datetime.now().isoformat(),
            "simulation_time": self.simulation_time,
            "objectList": [
                {   
                    "class_name": unit.name,
                    "confidence": unit.confidence,
                    "id": unit.id,
                    "type": unit.type,
                    "side": unit.side,
                    "position": {
                        "longitude": unit.position.longitude,
                        "latitude": unit.position.latitude,
                        "altitude": unit.position.altitude
                    },
                    "status": {
                        "health": unit.status.health,
                        "ammo": unit.status.ammo,
                        "fuel": unit.status.fuel,
                        "operational": unit.status.operational
                    },
                    "threat_level": unit.threat_level,
                    "last_seen": unit.last_seen,
                    "speed": unit.speed,
                    "heading": unit.heading
                } for unit in self.enemy_units
            ],
            "friendly_units": [
                {
                    "id": unit.id,
                    "name": unit.name,
                    "type": unit.type,
                    "side": unit.side,
                    "position": {
                        "longitude": unit.position.longitude,
                        "latitude": unit.position.latitude,
                        "altitude": unit.position.altitude
                    },
                    "status": {
                        "health": unit.status.health,
                        "ammo": unit.status.ammo,
                        "fuel": unit.status.fuel,
                        "operational": unit.status.operational
                    },
                    "threat_level": unit.threat_level,
                    "confidence": unit.confidence,
                    "last_seen": unit.last_seen,
                    "speed": unit.speed,
                    "heading": unit.heading
                } for unit in self.friendly_units
            ]
        }

    async def _send_threed_data_to_api(self, data: Dict[str, Any]):
        """发送三维显示数据到API和MQTT"""
        # 发送到HTTP API
        try:
            response = requests.post(
                f"{self.api_url}/threed/units",
                json=data,
                timeout=5  # 三维数据发送超时时间较短
            )

            if response.status_code == 200:
                logger.debug(f"✅ 三维数据发送成功")
            else:
                logger.warning(f"❌ 三维数据发送失败 - 状态码: {response.status_code}")

        except requests.exceptions.Timeout:
            logger.warning("❌ 三维数据请求超时")
        except requests.exceptions.ConnectionError:
            logger.warning("❌ 三维数据连接错误")
        except Exception as e:
            logger.warning(f"❌ 发送三维数据时发生错误: {e}")

        # 发送到MQTT
        self._publish_to_mqtt(
            self.mqtt_config["topics"]["threed_units"],
            data,
            "三维显示数据"
        )

    def _send_unit_status_to_mqtt(self, unit: BattlefieldUnit):
        """发送单个单位状态到MQTT"""
        unit_data = {
            "timestamp": datetime.now().isoformat(),
            "unit": asdict(unit)
        }

        # 发送到单位状态主题
        topic = f"{self.mqtt_config['topics']['unit_status']}/{unit.side}/{unit.id}"
        self._publish_to_mqtt(topic, unit_data, f"单位状态({unit.name})")

    async def start_simulation(self, analysis_interval_seconds: int = 30, threed_interval_seconds: float = 2.4):
        """
        开始模拟

        Args:
            analysis_interval_seconds: 发送给大模型分析的间隔（秒）
            threed_interval_seconds: 发送给三维显示的间隔（秒），默认2.4秒（每分钟25次）
        """
        self.running = True
        logger.info(f"开始战场模拟")
        logger.info(f"大模型分析数据发送间隔: {analysis_interval_seconds}秒")
        logger.info(f"三维显示数据发送间隔: {threed_interval_seconds}秒")

        # 记录时间
        last_analysis_time = 0
        last_threed_time = 0
        start_time = time.time()

        while self.running:
            try:
                current_time = time.time() - start_time

                # 检查是否需要发送分析数据（每分钟一次）
                if current_time - last_analysis_time >= analysis_interval_seconds:
                    # 更新模拟状态
                    self.update_simulation()

                    # 创建并发送分析数据
                    battlefield_data = self._create_battlefield_data()
                    await self._send_data_to_api(battlefield_data)

                    last_analysis_time = current_time
                    logger.info(f"📊 已发送分析数据 - 模拟时间: {self.simulation_time:.1f}分钟")

                # 检查是否需要发送三维显示数据（每分钟25次）
                if current_time - last_threed_time >= threed_interval_seconds:
                    # 轻微更新单位位置（用于三维显示的平滑移动）
                    self._update_units_for_threed(threed_interval_seconds / 60.0)  # 转换为分钟

                    # 创建并发送三维显示数据
                    threed_data = self._create_threed_data()
                    await self._send_threed_data_to_api(threed_data)

                    last_threed_time = current_time

                # 短暂休眠，避免CPU占用过高
                await asyncio.sleep(0.1)

            except KeyboardInterrupt:
                logger.info("收到停止信号，正在停止模拟...")
                break
            except Exception as e:
                logger.error(f"模拟过程中发生错误: {e}")
                await asyncio.sleep(5)  # 错误后等待5秒再继续

    def _update_units_for_threed(self, time_delta_minutes: float):
        """为三维显示更新单位位置（轻微移动）"""
        for unit in self.enemy_units + self.friendly_units:
            if unit.status.operational:
                # 轻微的位置更新，用于三维显示的平滑移动
                self._update_unit_position(unit, time_delta_minutes * 0.1)  # 减少移动幅度
    
    def stop_simulation(self):
        """停止模拟"""
        self.running = False

        # 断开MQTT连接
        if self.mqtt_client:
            try:
                self.mqtt_client.loop_stop()
                self.mqtt_client.disconnect()
                logger.info("MQTT连接已断开")
            except Exception as e:
                logger.error(f"断开MQTT连接时发生错误: {e}")

        logger.info("模拟已停止")

async def main():
    """主程序"""
    # 创建模拟器（MQTT配置从config/mqtt_config.json加载）
    simulator = BattlefieldSimulator()

    # 开始模拟
    # 分析数据: 每30秒发送一次
    # 三维显示数据: 每2.4秒发送一次（每分钟25次）
    try:
        logger.info("=== 战场模拟器启动 ===")
        logger.info("数据发送方式:")
        logger.info("  - HTTP API: 战场分析和三维显示数据")
        logger.info("  - MQTT: 实时数据流")
        logger.info(f"  - MQTT代理: {simulator.mqtt_config['broker_host']}:{simulator.mqtt_config['broker_port']}")
        logger.info("主题配置:")
        for topic_name, topic_path in simulator.mqtt_config['topics'].items():
            logger.info(f"  - {topic_name}: {topic_path}")

        await simulator.start_simulation(
            analysis_interval_seconds=30,  # 大模型分析数据间隔
            threed_interval_seconds=2.4    # 三维显示数据间隔（60/25=2.4秒）
        )
    except KeyboardInterrupt:
        simulator.stop_simulation()
        logger.info("程序已退出")

if __name__ == "__main__":
    asyncio.run(main())
