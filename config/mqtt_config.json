{"mqtt": {"broker_host": "localhost", "broker_port": 1883, "username": null, "password": null, "keepalive": 60, "client_id": "battlefield_simulator", "clean_session": true}, "topics": {"battlefield_analysis": "battlefield/analysis", "threed_units": "battlefield/threed/units", "unit_status": "battlefield/units/status", "system_status": "battlefield/system/status"}, "message_settings": {"qos": 1, "retain": false, "max_payload_size": 1048576}, "publishing": {"enable_battlefield_analysis": true, "enable_threed_units": true, "enable_unit_status": true, "enable_system_status": false, "batch_size": 1, "publish_interval_ms": 100}, "connection": {"auto_reconnect": true, "max_reconnect_attempts": 5, "reconnect_delay_seconds": 5, "connection_timeout": 30}, "logging": {"enable_mqtt_logs": true, "log_level": "INFO", "log_publish_success": false, "log_connection_events": true}}