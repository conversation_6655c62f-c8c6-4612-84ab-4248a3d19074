{"llm_providers": {"openai_compatible": {"provider": "openai_compatible", "model": "Qwen3", "api_key": "sk", "base_url": "http://localhost:8000/v1", "max_tokens": 4000, "temperature": 0.3, "timeout": 60, "retry_attempts": 5, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {api_key}"}}, "local_model": {"provider": "local", "model": "local-llm", "base_url": "http://localhost:8000/v1", "api_key": "local-key", "max_tokens": 4000, "temperature": 0.3, "timeout": 60, "retry_attempts": 2, "headers": {"Content-Type": "application/json"}}, "mock": {"provider": "mock", "model": "mock-llm", "max_tokens": 4000, "temperature": 0.3, "timeout": 5}}, "default_provider": "openai_compatible", "fallback_provider": "local_model", "pipeline_config": {"enable_caching": true, "max_concurrent_requests": 5, "timeout_seconds": 60, "retry_attempts": 3, "retry_delay_seconds": 1}, "prompt_config": {"max_prompt_length": 32000, "include_technical_specs": true, "include_historical_data": false, "language": "zh-CN"}}